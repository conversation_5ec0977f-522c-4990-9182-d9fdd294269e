<template>
  <div class="standalone-world-map" :style="containerStyle">
    <div class="map-title" v-if="showTitle">
      <span class="title-text">{{ title }}</span>
    </div>
    <div class="map-container" :class="{ 'show-grid': showGrid }">
      <!-- 背景图片容器 (Background image container) -->
      <div class="map-background-container"></div>
      <!-- Mapbox地图容器 (Mapbox map container) -->
      <div :id="mapId" class="mapbox-map" :style="mapContainerStyle"></div>
    </div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl'

export default {
  name: 'StandaloneWorldMap',
  props: {
    // Map configuration
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '500px'
    },
    title: {
      type: String,
      default: 'Global Distribution'
    },
    showTitle: {
      type: Boolean,
      default: true
    },

    // Map behavior
    center: {
      type: Array,
      default: () => [20, 0]
    },
    zoom: {
      type: Number,
      default: 2
    },
    // 区域模式配置 (Regional mode configuration)
    regionalMode: {
      type: Boolean,
      default: false
    },
    // 区域配置对象 (Regional configuration object)
    regionConfig: {
      type: Object,
      default: () => ({
        name: 'global',
        center: [20, 0],
        zoom: 2,
        bounds: null
      })
    },

    // Path data
    pathData: {
      type: Array,
      default: () => []
    },

    // Styling options
    colors: {
      type: Object,
      default: () => ({
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#1ED9B5',
        sourcePoint: '#1ED9B5',
        targetPoint: '#1ED9B5'
      })
    },

    // Animation settings
    animationEnabled: {
      type: Boolean,
      default: true
    },
    animationSpeed: {
      type: Number,
      default: 2000
    },
    flowAnimationSpeed: {
      type: Number,
      default: 0.002, // Speed of flow particles along paths (further reduced for slower, more deliberate animation)
      validator: (value) => value > 0 && value <= 0.1
    },
    flowParticleCount: {
      type: Number,
      default: 1, // Number of flow particles per path (single particle for cleaner effect)
      validator: (value) => value >= 1 && value <= 10
    },

    // Grid overlay
    showGrid: {
      type: Boolean,
      default: true
    },

    // 最小化模式 - 隐藏文本标签和边界 (Minimalist mode - hide text labels and borders)
    minimalistMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      flowAnimations: [], // Store flow animation data
      mapboxLoaded: false,
      mapId: `world-map-${Math.random().toString(36).substr(2, 9)}`,
      // Mapbox GL JS 配置 (Mapbox GL JS configuration)
      mapboxAccessToken: 'pk.eyJ1IjoiYmFyNXhjIiwiYSI6ImNtYXdhdzcwZDBiajUydHIycjh0OXZrYW8ifQ.1pfjx8FkKHbR4n94jINJNw',
      mapboxStyleUrl: 'mapbox://styles/bar5xc/cmbt2sn75012001s572du2owy',
      // 动画和图层管理 (Animation and layer management)
      animationFrameIds: [],
      pathSources: new Map(),
      markerElements: new Map(),
      // 样式刷新相关数据 (Style refresh related data)
      isStyleRefreshing: false, // 样式刷新状态标志 (Style refresh status flag)
      preservedMapState: null, // 保存的地图状态 (Preserved map state)
      preservedLayers: [], // 保存的自定义图层 (Preserved custom layers)
      preservedSources: new Map() // 保存的数据源 (Preserved data sources)
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    mapContainerStyle() {
      return {
        backgroundColor: this.colors.ocean
      }
    },
    // 计算实际使用的地图中心点 (Calculate actual map center to use)
    // Mapbox uses [longitude, latitude] format
    actualMapCenter() {
      const center = this.regionalMode && this.regionConfig.center
        ? this.regionConfig.center
        : this.center
      // Convert from [lat, lng] to [lng, lat] for Mapbox
      return [center[1], center[0]]
    },
    // 计算实际使用的缩放级别 (Calculate actual zoom level to use)
    actualMapZoom() {
      return this.regionalMode && this.regionConfig.zoom
        ? this.regionConfig.zoom
        : this.zoom
    }
  },
  mounted() {
    this.initMapbox()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    pathData: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          this.renderPaths()
        }
      },
      deep: true
    },
    animationEnabled: {
      handler(newValue) {
        if (!newValue) {
          // Stop all flow animations when animation is disabled
          this.flowAnimations.forEach(animationData => {
            if (animationData.animationFrameId) {
              cancelAnimationFrame(animationData.animationFrameId)
              animationData.animationFrameId = null
            }
            animationData.isActive = false
          })
        } else if (this.map && this.mapboxLoaded) {
          // Restart animations when re-enabled
          this.renderPaths()
        }
      }
    },
    // 监听区域模式变化 (Watch for regional mode changes)
    regionalMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          // 重新设置地图视图 (Reset map view)
          this.updateMapView()
        }
      }
    },

    // 监听简洁模式变化 (Watch for minimalist mode changes)
    minimalistMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          console.log('简洁模式切换 (Minimalist mode toggled):', this.minimalistMode)
          // 重新渲染路径 (Re-render paths)
          this.renderPaths()
        }
      }
    },
    // 监听区域配置变化 (Watch for region config changes)
    regionConfig: {
      handler() {
        if (this.map && this.mapboxLoaded && this.regionalMode) {
          // 更新地图视图 (Update map view)
          this.updateMapView()
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化Mapbox GL JS地图 (Initialize Mapbox GL JS map)
     */
    initMapbox() {
      console.log('开始初始化Mapbox地图 (Starting Mapbox map initialization)')
      console.log('访问令牌 (Access token):', this.mapboxAccessToken.substring(0, 20) + '...')
      console.log('样式URL (Style URL):', this.mapboxStyleUrl)

      // 设置Mapbox访问令牌 (Set Mapbox access token)
      mapboxgl.accessToken = this.mapboxAccessToken

      // 确保Mapbox CSS已加载 (Ensure Mapbox CSS is loaded)
      this.loadMapboxCSS()

      try {
        // 创建地图实例 - 使用样式中的默认视图设置 (Create map instance - use default view settings from style)
        this.map = new mapboxgl.Map({
          container: this.mapId,
          style: this.mapboxStyleUrl,
          // 不设置center和zoom，让样式配置处理 (Don't set center and zoom, let style configuration handle it)
          interactive: false, // 禁用交互以匹配原始行为 (Disable interaction to match original behavior)
          attributionControl: false,
          logoPosition: 'bottom-right'
        })
        console.log('Mapbox地图实例创建成功 (Mapbox map instance created successfully)')
      } catch (error) {
        console.error('创建Mapbox地图实例失败 (Failed to create Mapbox map instance):', error)
        return
      }

      // 地图加载完成后的处理 (Handle map load completion)
      this.map.on('load', () => {
        console.log('Mapbox地图加载完成 (Mapbox map loaded)')
        this.mapboxLoaded = true

        // 记录当前地图视图信息 (Log current map view info)
        console.log('地图当前中心点 (Current map center):', this.map.getCenter())
        console.log('地图当前缩放级别 (Current map zoom):', this.map.getZoom())

        // 渲染路径数据 (Render path data)
        if (this.pathData.length > 0) {
          this.renderPaths()
        }

        // 发出地图就绪事件 (Emit map ready event)
        this.$emit('map-ready', this.map)
      })

      // 错误处理 (Error handling)
      this.map.on('error', (e) => {
        console.error('Mapbox地图加载错误 (Mapbox map load error):', e)
        console.error('错误详情 (Error details):', {
          error: e.error,
          sourceId: e.sourceId,
          isSourceLoaded: e.isSourceLoaded
        })
      })

      // 样式加载事件 (Style load events)
      this.map.on('styledata', () => {
        console.log('Mapbox样式数据加载完成 (Mapbox style data loaded)')
      })

      this.map.on('sourcedataloading', (e) => {
        console.log('Mapbox数据源开始加载 (Mapbox source data loading):', e.sourceId)
      })

      this.map.on('sourcedata', (e) => {
        console.log('Mapbox数据源加载完成 (Mapbox source data loaded):', e.sourceId)
      })

      // 添加点击事件用于调试 (Add click event for debugging)
      this.map.on('click', (e) => {
        console.log('地图点击位置 (Map click position):', e.lngLat)
      })
    },

    /**
     * 强制设置地图背景透明 (Force map background transparency)
     */
    enforceMapTransparency() {
      if (!this.map) return

      try {
        // 获取地图容器和画布元素 (Get map container and canvas elements)
        const mapContainer = this.map.getContainer()
        const canvasContainer = mapContainer.querySelector('.mapboxgl-canvas-container')
        const canvas = mapContainer.querySelector('.mapboxgl-canvas')

        // 强制设置透明背景 (Force transparent background)
        if (canvasContainer) {
          canvasContainer.style.backgroundColor = 'transparent'
          canvasContainer.style.background = 'transparent'
        }

        if (canvas) {
          canvas.style.backgroundColor = 'transparent'
          canvas.style.background = 'transparent'
        }

        // 设置地图容器本身透明 (Set map container itself transparent)
        if (mapContainer) {
          mapContainer.style.backgroundColor = 'transparent'
          mapContainer.style.background = 'transparent'
        }

        // 设置Mapbox样式中的背景图层透明度 (Set background layer opacity in Mapbox style)
        this.setMapboxBackgroundOpacity()

        console.log('地图背景透明度已强制设置 (Map background transparency enforced)')
      } catch (error) {
        console.warn('设置地图透明度失败 (Failed to set map transparency):', error)
      }
    },

    /**
     * 设置Mapbox样式中背景图层的透明度 (Set background layer opacity in Mapbox style)
     */
    setMapboxBackgroundOpacity() {
      if (!this.map) return

      try {
        // 获取地图样式 (Get map style)
        const style = this.map.getStyle()

        if (style && style.layers) {
          // 查找并修改背景类型的图层 (Find and modify background-type layers)
          style.layers.forEach(layer => {
            if (layer.type === 'background') {
              console.log('找到背景图层 (Found background layer):', layer.id)

              // 设置背景图层透明度为0 (Set background layer opacity to 0)
              this.map.setPaintProperty(layer.id, 'background-opacity', 0)
              console.log(`背景图层 ${layer.id} 透明度已设置为0 (Background layer ${layer.id} opacity set to 0)`)
            }
          })
        }

        // 也尝试设置常见的背景图层ID (Also try setting common background layer IDs)
        const commonBackgroundLayers = ['background', 'land', 'water', 'ocean']
        commonBackgroundLayers.forEach(layerId => {
          try {
            if (this.map.getLayer(layerId)) {
              this.map.setPaintProperty(layerId, 'background-opacity', 0)
              console.log(`通用背景图层 ${layerId} 透明度已设置 (Common background layer ${layerId} opacity set)`)
            }
          } catch (e) {
            // 忽略不存在的图层 (Ignore non-existent layers)
          }
        })

      } catch (error) {
        console.warn('设置Mapbox背景图层透明度失败 (Failed to set Mapbox background layer opacity):', error)
      }
    },

    /**
     * 刷新Mapbox样式 (Refresh Mapbox style)
     * @param {string} newStyleUrl - 新的样式URL (可选) (New style URL - optional)
     * @param {boolean} forceRefresh - 强制刷新缓存 (Force refresh cache)
     */
    async refreshMapStyle(newStyleUrl = null, forceRefresh = false) {
      if (!this.map || this.isStyleRefreshing) {
        console.warn('地图未初始化或正在刷新样式 (Map not initialized or style refresh in progress)')
        return false
      }

      console.log('开始刷新地图样式 (Starting map style refresh):', {
        currentStyle: this.mapboxStyleUrl,
        newStyle: newStyleUrl,
        forceRefresh
      })

      this.isStyleRefreshing = true

      try {
        // 1. 保存当前地图状态 (Save current map state)
        this.preserveMapState()

        // 2. 保存自定义图层和数据源 (Save custom layers and data sources)
        this.preserveCustomLayers()

        // 3. 确定要使用的样式URL (Determine style URL to use)
        const styleUrl = newStyleUrl || this.mapboxStyleUrl
        const finalStyleUrl = forceRefresh ? `${styleUrl}?t=${Date.now()}` : styleUrl

        // 4. 更新样式URL (Update style URL)
        if (newStyleUrl) {
          this.mapboxStyleUrl = newStyleUrl
        }

        // 5. 设置新样式 (Set new style)
        await this.setMapStyle(finalStyleUrl)

        console.log('地图样式刷新成功 (Map style refresh successful)')
        return true

      } catch (error) {
        console.error('地图样式刷新失败 (Map style refresh failed):', error)
        return false
      } finally {
        this.isStyleRefreshing = false
      }
    },

    /**
     * 设置地图样式 (Set map style)
     */
    async setMapStyle(styleUrl) {
      return new Promise((resolve, reject) => {
        // 设置样式加载超时 (Set style loading timeout)
        const timeout = setTimeout(() => {
          reject(new Error('样式加载超时 (Style loading timeout)'))
        }, 30000) // 30秒超时

        // 监听样式加载完成 (Listen for style load completion)
        const onStyleLoad = () => {
          clearTimeout(timeout)
          this.map.off('styledata', onStyleLoad)
          this.map.off('error', onStyleError)

          // 样式加载完成后恢复状态 (Restore state after style load)
          this.restoreMapState()
          resolve()
        }

        // 监听样式加载错误 (Listen for style load errors)
        const onStyleError = (error) => {
          clearTimeout(timeout)
          this.map.off('styledata', onStyleLoad)
          this.map.off('error', onStyleError)
          reject(error)
        }

        // 添加事件监听器 (Add event listeners)
        this.map.on('styledata', onStyleLoad)
        this.map.on('error', onStyleError)

        // 设置新样式 (Set new style)
        this.map.setStyle(styleUrl)
      })
    },

    /**
     * 保存地图状态 (Preserve map state)
     */
    preserveMapState() {
      if (!this.map) return

      this.preservedMapState = {
        center: this.map.getCenter(),
        zoom: this.map.getZoom(),
        bearing: this.map.getBearing(),
        pitch: this.map.getPitch()
      }

      console.log('地图状态已保存 (Map state preserved):', this.preservedMapState)
    },

    /**
     * 保存自定义图层和数据源 (Preserve custom layers and data sources)
     */
    preserveCustomLayers() {
      if (!this.map) return

      this.preservedLayers = []
      this.preservedSources.clear()

      try {
        const style = this.map.getStyle()

        if (style) {
          // 保存自定义图层 (Save custom layers)
          if (style.layers) {
            style.layers.forEach(layer => {
              // 只保存我们添加的自定义图层 (Only save custom layers we added)
              if (layer.id.includes('path-') || layer.id.includes('trail-')) {
                this.preservedLayers.push({
                  id: layer.id,
                  type: layer.type,
                  source: layer.source,
                  layout: layer.layout,
                  paint: layer.paint,
                  beforeId: null // 将在恢复时确定 (Will be determined during restoration)
                })
              }
            })
          }

          // 保存自定义数据源 (Save custom data sources)
          if (style.sources) {
            Object.keys(style.sources).forEach(sourceId => {
              if (sourceId.includes('path-') || sourceId.includes('trail-')) {
                this.preservedSources.set(sourceId, style.sources[sourceId])
              }
            })
          }
        }

        console.log('自定义图层和数据源已保存 (Custom layers and sources preserved):', {
          layers: this.preservedLayers.length,
          sources: this.preservedSources.size
        })

      } catch (error) {
        console.warn('保存自定义图层失败 (Failed to preserve custom layers):', error)
      }
    },

    /**
     * 恢复地图状态 (Restore map state)
     */
    async restoreMapState() {
      if (!this.map) return

      try {
        // 1. 恢复地图视图状态 (Restore map view state)
        if (this.preservedMapState) {
          this.map.setCenter(this.preservedMapState.center)
          this.map.setZoom(this.preservedMapState.zoom)
          this.map.setBearing(this.preservedMapState.bearing)
          this.map.setPitch(this.preservedMapState.pitch)
          console.log('地图视图状态已恢复 (Map view state restored)')
        }

        // 2. 强制设置背景透明 (Force background transparency)
        this.enforceMapTransparency()

        // 3. 恢复自定义数据源 (Restore custom data sources)
        this.preservedSources.forEach((sourceData, sourceId) => {
          try {
            if (!this.map.getSource(sourceId)) {
              this.map.addSource(sourceId, sourceData)
            }
          } catch (error) {
            console.warn(`恢复数据源失败 (Failed to restore source) ${sourceId}:`, error)
          }
        })

        // 4. 恢复自定义图层 (Restore custom layers)
        this.preservedLayers.forEach(layerData => {
          try {
            if (!this.map.getLayer(layerData.id)) {
              this.map.addLayer({
                id: layerData.id,
                type: layerData.type,
                source: layerData.source,
                layout: layerData.layout,
                paint: layerData.paint
              }, layerData.beforeId)
            }
          } catch (error) {
            console.warn(`恢复图层失败 (Failed to restore layer) ${layerData.id}:`, error)
          }
        })

        // 5. 重新渲染路径和动画 (Re-render paths and animations)
        if (this.pathData.length > 0) {
          // 延迟一点时间确保样式完全加载 (Delay to ensure style is fully loaded)
          setTimeout(() => {
            this.renderPaths()
          }, 500)
        }

        // 6. 应用区域边界（如果需要）(Apply regional bounds if needed)
        if (this.regionalMode && this.regionConfig.bounds) {
          setTimeout(() => {
            this.applyRegionalBounds()
          }, 1000)
        }

        console.log('地图状态恢复完成 (Map state restoration completed)')

      } catch (error) {
        console.error('恢复地图状态失败 (Failed to restore map state):', error)
      }
    },

    /**
     * 完全重新初始化地图 (Complete map reinitialization)
     */
    async reinitializeMap() {
      console.log('开始完全重新初始化地图 (Starting complete map reinitialization)')

      try {
        // 1. 清理现有地图 (Cleanup existing map)
        this.cleanup()

        // 2. 重置状态 (Reset state)
        this.mapboxLoaded = false
        this.map = null

        // 3. 延迟一点时间确保清理完成 (Delay to ensure cleanup is complete)
        await new Promise(resolve => setTimeout(resolve, 500))

        // 4. 重新初始化 (Reinitialize)
        this.initMapbox()

        console.log('地图重新初始化完成 (Map reinitialization completed)')
        return true

      } catch (error) {
        console.error('地图重新初始化失败 (Map reinitialization failed):', error)
        return false
      }
    },

    /**
     * 加载Mapbox CSS样式 (Load Mapbox CSS styles)
     */
    loadMapboxCSS() {
      if (!document.querySelector('link[href*="mapbox-gl.css"]')) {
        const mapboxCss = document.createElement('link')
        mapboxCss.rel = 'stylesheet'
        mapboxCss.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
        document.head.appendChild(mapboxCss)
      }
    },

    /**
     * 渲染路径数据到Mapbox地图 (Render path data to Mapbox map)
     */
    renderPaths() {
      if (!this.map || !this.mapboxLoaded) return

      console.log('开始渲染路径 (Starting to render paths):', this.pathData.length)

      // 清除现有的路径和动画 (Clear existing paths and animations)
      this.clearMapLayers()

      // 为每条路径创建数据源和图层 (Create data sources and layers for each path)
      this.pathData.forEach((path, index) => {
        this.renderSinglePath(path, index)
      })
    },

    /**
     * 渲染单条路径到Mapbox地图 (Render single path to Mapbox map)
     */
    renderSinglePath(path, index) {
      if (!path.coords || path.coords.length < 2) {
        console.warn(`路径 ${index} 坐标无效 (Path ${index} has invalid coordinates):`, path)
        return
      }

      const [sourceCoords, targetCoords] = path.coords
      const pathId = `path-${index}`
      const sourceId = `source-${index}`
      const targetId = `target-${index}`

      console.log(`渲染路径 ${index} (Rendering path ${index}):`, {
        name: path.name,
        sourceCoords,
        targetCoords,
        pathId
      })

      // 生成曲线路径点 (Generate curved path points)
      const curvePoints = this.generateCurvePoints(sourceCoords, targetCoords)

      // 转换坐标格式为Mapbox格式 [lng, lat] (Convert coordinates to Mapbox format [lng, lat])
      const mapboxCurvePoints = curvePoints.map(point => [point[1], point[0]])

      console.log(`路径 ${index} 曲线点数量 (Path ${index} curve points count):`, mapboxCurvePoints.length)

      // 创建路径线的GeoJSON数据 (Create GeoJSON data for path line)
      const pathGeoJSON = {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: mapboxCurvePoints
        },
        properties: {
          name: path.name || `Path ${index}`,
          value: path.value || 50
        }
      }

      try {
        // 添加路径数据源 (Add path data source)
        if (!this.map.getSource(pathId)) {
          this.map.addSource(pathId, {
            type: 'geojson',
            data: pathGeoJSON
          })
          console.log(`路径数据源已添加 (Path data source added): ${pathId}`)
        }

        // 添加路径线图层 (Add path line layer)
        if (!this.map.getLayer(`${pathId}-line`)) {
          this.map.addLayer({
            id: `${pathId}-line`,
            type: 'line',
            source: pathId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': this.colors.pathLine,
              'line-width': 2,
              'line-opacity': 0.8
            }
          })
          console.log(`路径线图层已添加 (Path line layer added): ${pathId}-line`)

          // 添加发光效果 (Add glow effect)
          this.map.addLayer({
            id: `${pathId}-glow`,
            type: 'line',
            source: pathId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': this.colors.pathLine,
              'line-width': 6,
              'line-opacity': 0.3,
              'line-blur': 2
            }
          }, `${pathId}-line`) // 将发光层放在线条层下方 (Place glow layer below line layer)
          console.log(`路径发光图层已添加 (Path glow layer added): ${pathId}-glow`)
        }
      } catch (error) {
        console.error(`添加路径图层失败 (Failed to add path layers) ${pathId}:`, error)
        return
      }

      // 创建起点和终点标记 (Create source and target markers)
      this.createMarker(sourceCoords, sourceId, 'source', path, index)
      this.createMarker(targetCoords, targetId, 'target', path, index)

      // 存储路径信息 (Store path information)
      this.pathSources.set(pathId, {
        sourceId: pathId,
        layerIds: [`${pathId}-line`, `${pathId}-glow`],
        curvePoints: curvePoints,
        pathData: path
      })

      // 如果启用动画，创建流动动画 (If animation enabled, create flow animation)
      if (this.animationEnabled) {
        this.createFlowAnimation(curvePoints, index, pathId)
      }
    },

    /**
     * 创建标记点 (Create marker)
     */
    createMarker(coords, markerId, type, pathData, index) {
      // 创建标记元素 (Create marker element)
      const markerElement = document.createElement('div')
      markerElement.className = `marker ${type}-marker`
      markerElement.style.cssText = `
        width: ${Math.max(8, (pathData.value || 50) / 30)}px;
        height: ${Math.max(8, (pathData.value || 50) / 30)}px;
        background-color: ${this.colors[type + 'Point']};
        border: 2px solid ${this.colors[type + 'Point']};
        border-radius: 50%;
        box-shadow: 0 0 10px ${this.colors[type + 'Point']};
        cursor: pointer;
        transition: all 0.3s ease;
      `

      // 创建Mapbox标记 (Create Mapbox marker)
      const marker = new mapboxgl.Marker(markerElement)
        .setLngLat([coords[1], coords[0]]) // Mapbox uses [lng, lat]
        .addTo(this.map)

      // 添加点击事件和提示信息 (Add click event and tooltip)
      if (pathData.name) {
        const tooltipText = type === 'source'
          ? pathData.name.split(' to ')[0] || 'Source'
          : pathData.name.split(' to ')[1] || 'Target'

        markerElement.title = tooltipText
      }

      // 存储标记引用 (Store marker reference)
      this.markerElements.set(markerId, {
        marker: marker,
        element: markerElement,
        type: type,
        coords: coords,
        pathData: pathData
      })

      // 如果启用动画，开始标记动画 (If animation enabled, start marker animation)
      if (this.animationEnabled) {
        this.animateMarker(markerElement, index + (type === 'target' ? 0.5 : 0))
      }

      return marker
    },

    /**
     * 生成曲线路径点 (Generate curved path points)
     */
    generateCurvePoints(start, end, numPoints = 20) {
      const points = []

      // Calculate the midpoint
      const midLat = (start[0] + end[0]) / 2
      const midLng = (start[1] + end[1]) / 2

      // Calculate the distance for curve height
      const distance = Math.sqrt(
        Math.pow(end[0] - start[0], 2) + Math.pow(end[1] - start[1], 2)
      )

      // Add curvature based on distance
      const curvature = distance * 0.3

      // Determine if we should curve up or down based on hemisphere
      const curveDirection = midLat > 0 ? 1 : -1
      const controlPoint = [midLat + (curvature * curveDirection), midLng]

      // Generate points along the quadratic bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints
        const lat = Math.pow(1 - t, 2) * start[0] + 2 * (1 - t) * t * controlPoint[0] + Math.pow(t, 2) * end[0]
        const lng = Math.pow(1 - t, 2) * start[1] + 2 * (1 - t) * t * controlPoint[1] + Math.pow(t, 2) * end[1]
        points.push([lat, lng])
      }

      return points
    },

    /**
     * 动画标记点 (Animate marker)
     */
    animateMarker(markerElement, index) {
      if (!markerElement || !this.animationEnabled) return

      let opacity = 0.3
      let increasing = true

      const animate = () => {
        if (increasing) {
          opacity += 0.015
          if (opacity >= 1) {
            increasing = false
          }
        } else {
          opacity -= 0.015
          if (opacity <= 0.3) {
            increasing = true
          }
        }

        // 更新标记元素的透明度和发光效果 (Update marker element opacity and glow effect)
        markerElement.style.opacity = opacity
        markerElement.style.boxShadow = `0 0 ${10 + opacity * 10}px ${markerElement.style.backgroundColor}`

        // 继续动画如果标记仍然有效且动画已启用 (Continue animation if marker is still valid and animation is enabled)
        if (markerElement.parentNode && this.animationEnabled) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }

      // 根据索引延迟开始动画 (Start animation with delay based on index)
      setTimeout(() => {
        if (this.animationEnabled && markerElement.parentNode) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }, index * 150)
    },

    /**
     * 创建流动动画 (Create flow animation)
     */
    createFlowAnimation(curvePoints, pathIndex, pathId) {
      if (!this.map || !this.animationEnabled || curvePoints.length < 2) return

      // 创建多个流动粒子以获得更好的视觉效果 (Create multiple flow particles for better visual effect)
      const numParticles = this.flowParticleCount
      const particles = []

      for (let i = 0; i < numParticles; i++) {
        // 创建粒子元素 (Create particle element)
        const particleElement = document.createElement('div')
        particleElement.className = 'flow-particle'
        // 使用白色粒子与绿色路径形成对比 (Use white particles to contrast with green paths)
        const particleColor = '#FFFFFF' // 白色粒子与绿色路径形成清晰对比 (White particles create clear contrast with green paths)
        particleElement.style.cssText = `
          width: 6px;
          height: 6px;
          background-color: ${particleColor};
          border: 1px solid ${particleColor};
          border-radius: 50%;
          box-shadow: 0 0 8px ${particleColor};
          position: absolute;
          pointer-events: none;
          z-index: 1000;
        `

        // 创建Mapbox标记 (Create Mapbox marker)
        const particleMarker = new mapboxgl.Marker(particleElement)
          .setLngLat([curvePoints[0][1], curvePoints[0][0]]) // 从起点开始 (Start from beginning)
          .addTo(this.map)

        particles.push({
          marker: particleMarker,
          element: particleElement,
          progress: 0, // 所有粒子从起点开始，实现顺序流动 (All particles start from beginning for sequential flow)
          pathIndex: pathIndex,
          isActive: i === 0 // 只有第一个粒子开始时是活跃的 (Only first particle is active initially)
        })
      }

      // 动画状态 (Animation state)
      const animationData = {
        particles: particles,
        curvePoints: curvePoints,
        pathIndex: pathIndex,
        pathId: pathId,
        animationFrameId: null,
        isActive: true
      }

      this.flowAnimations.push(animationData)

      // 动画函数 (Animation function)
      const animateFlow = () => {
        if (!this.animationEnabled || !animationData.isActive) {
          return
        }

        let allParticlesValid = true

        // 实现顺序粒子流动：一次只有一个粒子活跃 (Implement sequential particle flow: only one particle active at a time)
        let activeParticleIndex = -1
        let currentActiveParticle = null

        // 找到当前活跃的粒子 (Find currently active particle)
        for (let i = 0; i < animationData.particles.length; i++) {
          if (animationData.particles[i].isActive) {
            activeParticleIndex = i
            currentActiveParticle = animationData.particles[i]
            break
          }
        }

        // 如果没有活跃粒子，激活第一个 (If no active particle, activate the first one)
        if (currentActiveParticle === null) {
          animationData.particles[0].isActive = true
          currentActiveParticle = animationData.particles[0]
          activeParticleIndex = 0
        }

        // 处理当前活跃的粒子 (Process the currently active particle)
        if (currentActiveParticle && currentActiveParticle.element.parentNode) {
          // 更新粒子进度 (Update particle progress)
          currentActiveParticle.progress += this.flowAnimationSpeed

          // 当粒子完成路径时，切换到下一个粒子 (When particle completes path, switch to next particle)
          if (currentActiveParticle.progress >= 1) {
            // 隐藏当前粒子 (Hide current particle)
            currentActiveParticle.element.style.opacity = 0
            currentActiveParticle.isActive = false
            currentActiveParticle.progress = 0

            // 激活下一个粒子（循环到第一个）(Activate next particle, cycling back to first)
            const nextIndex = (activeParticleIndex + 1) % animationData.particles.length

            // 添加延迟后激活下一个粒子 (Add delay before activating next particle)
            setTimeout(() => {
              if (animationData.isActive && this.animationEnabled) {
                animationData.particles[nextIndex].isActive = true
                animationData.particles[nextIndex].progress = 0
              }
            }, 1000) // 1秒延迟创建间隔效果 (1 second delay creates spacing effect)
          } else {
            // 计算沿曲线的位置 (Calculate position along the curve)
            const position = this.getPositionAlongPath(curvePoints, currentActiveParticle.progress)
            if (position) {
              try {
                // 更新粒子位置 (Update particle position)
                currentActiveParticle.marker.setLngLat([position[1], position[0]]) // Mapbox uses [lng, lat]

                // 根据进度添加淡入淡出效果 (Add fade effect based on progress)
                const opacity = Math.sin(currentActiveParticle.progress * Math.PI) * 0.7 + 0.3
                currentActiveParticle.element.style.opacity = opacity
                // 使用白色粒子的发光效果 (Use white particle glow effect)
                const particleColor = '#FFFFFF'
                currentActiveParticle.element.style.boxShadow = `0 0 ${8 * opacity}px ${particleColor}`
              } catch (e) {
                // 优雅地处理潜在错误 (Handle potential errors gracefully)
                console.warn('Flow animation error:', e)
                allParticlesValid = false
              }
            }
          }
        } else {
          allParticlesValid = false
        }

        // 隐藏所有非活跃粒子 (Hide all inactive particles)
        animationData.particles.forEach((particle) => {
          if (!particle.isActive && particle.element.parentNode) {
            particle.element.style.opacity = 0
          }
        })

        // 如果所有粒子都有效且动画已启用，则继续动画 (Continue animation if all particles are valid and animation is enabled)
        if (allParticlesValid && this.animationEnabled && animationData.isActive) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        } else {
          // 如果动画应该停止，则清理 (Clean up if animation should stop)
          this.cleanupFlowAnimation(animationData)
        }
      }

      // 根据路径索引延迟开始动画 (Start the animation with a delay based on path index)
      setTimeout(() => {
        if (this.animationEnabled && animationData.isActive) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        }
      }, pathIndex * 200)
    },

    /**
     * 获取沿路径的位置 (Get position along path)
     */
    getPositionAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算沿路径的确切位置 (Calculate the exact position along the path)
      const totalSegments = points.length - 1
      const segmentProgress = progress * totalSegments
      const segmentIndex = Math.floor(segmentProgress)
      const localProgress = segmentProgress - segmentIndex

      // 处理进度恰好为1的边缘情况 (Handle edge case where progress is exactly 1)
      if (segmentIndex >= totalSegments) {
        return points[points.length - 1]
      }

      // 在两点之间插值 (Interpolate between two points)
      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1]

      const lat = startPoint[0] + (endPoint[0] - startPoint[0]) * localProgress
      const lng = startPoint[1] + (endPoint[1] - startPoint[1]) * localProgress

      return [lat, lng]
    },

    /**
     * 清理流动动画 (Cleanup flow animation)
     */
    cleanupFlowAnimation(animationData) {
      if (!animationData) return

      // 取消动画帧 (Cancel animation frame)
      if (animationData.animationFrameId) {
        cancelAnimationFrame(animationData.animationFrameId)
        animationData.animationFrameId = null
      }

      // 从地图中移除粒子 (Remove particles from map)
      animationData.particles.forEach((particle) => {
        if (particle.marker && this.map) {
          try {
            particle.marker.remove()
          } catch (e) {
            // 在清理过程中忽略错误 (Ignore errors during cleanup)
          }
        }
      })

      // 标记为非活动状态 (Mark as inactive)
      animationData.isActive = false
    },

    /**
     * 清理地图图层 (Clear map layers)
     */
    clearMapLayers() {
      // 清理所有动画帧 (Clear all animation frames)
      this.animationFrameIds.forEach(id => {
        cancelAnimationFrame(id)
      })
      this.animationFrameIds = []

      // 清理路径数据源和图层 (Clear path data sources and layers)
      this.pathSources.forEach((pathInfo, pathId) => {
        // 移除图层 (Remove layers)
        pathInfo.layerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            this.map.removeLayer(layerId)
          }
        })

        // 移除数据源 (Remove data source)
        if (this.map.getSource(pathId)) {
          this.map.removeSource(pathId)
        }
      })
      this.pathSources.clear()

      // 清理标记 (Clear markers)
      this.markerElements.forEach((markerInfo) => {
        if (markerInfo.marker) {
          markerInfo.marker.remove()
        }
      })
      this.markerElements.clear()

      // 清理流动动画 (Clear flow animations)
      this.flowAnimations.forEach(animationData => {
        this.cleanupFlowAnimation(animationData)
      })
      this.flowAnimations = []

      // 清理旧的数组（向后兼容）(Clear old arrays for backward compatibility)
      this.pathLines = []
      this.sourceMarkers = []
      this.targetMarkers = []
    },

    /**
     * 组件清理 (Component cleanup)
     */
    cleanup() {
      // 清理所有图层 (Clear all layers)
      this.clearMapLayers()

      // 移除地图 (Remove map)
      if (this.map) {
        this.map.remove()
        this.map = null
      }

      // 重置状态 (Reset state)
      this.mapboxLoaded = false
    },

    /**
     * 应用区域边界限制 (Apply regional bounds restrictions)
     */
    applyRegionalBounds() {
      if (!this.map || !this.mapboxLoaded || !this.regionConfig.bounds) return

      try {
        const bounds = this.regionConfig.bounds
        const mapboxBounds = [
          [bounds.west, bounds.south], // 西南角 (Southwest corner)
          [bounds.east, bounds.north]  // 东北角 (Northeast corner)
        ]
        this.map.setMaxBounds(mapboxBounds)
        console.log('区域边界已应用 (Regional bounds applied):', bounds)
      } catch (error) {
        console.error('应用区域边界失败 (Failed to apply regional bounds):', error)
      }
    },

    /**
     * 更新地图视图 - 仅在模式切换时使用 (Update map view - only used when switching modes)
     */
    updateMapView() {
      if (!this.map || !this.mapboxLoaded) return

      console.log('更新地图视图 (Updating map view)')

      // 如果处于区域模式且有边界配置，应用边界 (If in regional mode with bounds config, apply bounds)
      if (this.regionalMode && this.regionConfig.bounds) {
        this.applyRegionalBounds()
      }
    },

    // 公共方法用于外部控制 (Public methods for external control)
    addPath(pathData) {
      const newPaths = Array.isArray(pathData) ? pathData : [pathData]
      this.$emit('update:pathData', [...this.pathData, ...newPaths])
    },

    removePath(index) {
      if (index >= 0 && index < this.pathData.length) {
        const newPaths = [...this.pathData]
        newPaths.splice(index, 1)
        this.$emit('update:pathData', newPaths)
      }
    },

    clearPaths() {
      this.$emit('update:pathData', [])
    },



    /**
     * 设置区域模式 (Set regional mode)
     * @param {Object} regionConfig - 区域配置对象 (Regional configuration object)
     */
    setRegionalMode(regionConfig) {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', true)
      this.$emit('update:regionConfig', { ...this.regionConfig, ...regionConfig })
      this.updateMapView()
    },

    /**
     * 切换到全球模式 (Switch to global mode)
     */
    setGlobalMode() {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', false)
      this.updateMapView()
    },

    // ========== 公共样式刷新方法 (Public Style Refresh Methods) ==========

    /**
     * 刷新当前样式 (Refresh current style)
     * 公共方法，可从组件外部调用 (Public method, can be called from outside the component)
     */
    async refreshCurrentStyle() {
      return await this.refreshMapStyle(null, true)
    },

    /**
     * 更新到新样式 (Update to new style)
     * 公共方法，可从组件外部调用 (Public method, can be called from outside the component)
     * @param {string} newStyleUrl - 新的样式URL (New style URL)
     */
    async updateMapStyle(newStyleUrl) {
      if (!newStyleUrl) {
        console.warn('新样式URL不能为空 (New style URL cannot be empty)')
        return false
      }
      return await this.refreshMapStyle(newStyleUrl, false)
    },

    /**
     * 强制刷新样式缓存 (Force refresh style cache)
     * 公共方法，可从组件外部调用 (Public method, can be called from outside the component)
     */
    async forceRefreshStyle() {
      return await this.refreshMapStyle(null, true)
    },

    /**
     * 重新初始化地图 (Reinitialize map)
     * 公共方法，可从组件外部调用 (Public method, can be called from outside the component)
     */
    async restartMap() {
      return await this.reinitializeMap()
    },

    /**
     * 获取当前样式URL (Get current style URL)
     * 公共方法，可从组件外部调用 (Public method, can be called from outside the component)
     */
    getCurrentStyleUrl() {
      return this.mapboxStyleUrl
    },

    /**
     * 检查样式是否正在刷新 (Check if style is refreshing)
     * 公共方法，可从组件外部调用 (Public method, can be called from outside the component)
     */
    isStyleRefreshInProgress() {
      return this.isStyleRefreshing
    }
  }
}
</script>

<style scoped>
.standalone-world-map {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: rgba(15, 46, 44, 0.9);
  /* 半透明以显示背景图片 (Semi-transparent to show background image) */
  border-radius: 8px;
  overflow: hidden;
  z-index: 10;
  /* 确保地图组件在背景图片和主页容器之上 (Ensure map component is above background image and home container) */
}

.map-title {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.title-text {
  color: #00FFCC;
  font-size: 16px;
  font-weight: 500;
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  background-color: transparent;
  /* 透明以显示背景图片 (Transparent to show background image) */
}

/* 背景图片容器 (Background image container) */
.map-background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../bg.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 1;
  /* 在Mapbox画布下方 (Below Mapbox canvas) */
  pointer-events: none;
  /* 不干扰地图交互 (Don't interfere with map interactions) */
}

/* Mapbox地图容器 (Mapbox map container) */
.mapbox-map {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  /* 在背景图片上方 (Above background image) */
}

/* Mapbox GL JS 样式覆盖 (Mapbox GL JS style overrides) */
.map-container :deep(.mapboxgl-canvas-container) {
  background-color: transparent !important;
  /* 透明以显示背景图片 (Transparent to show background image) */
  position: relative;
  z-index: 3;
  /* 在背景图片上方 (Above background image) */
}

.map-container :deep(.mapboxgl-canvas) {
  background-color: transparent !important;
  /* 透明以显示背景图片 (Transparent to show background image) */
  position: relative;
  z-index: 3;
  /* 在背景图片上方 (Above background image) */
}

/* 确保所有Mapbox相关元素透明 (Ensure all Mapbox related elements are transparent) */
.map-container :deep(.mapboxgl-map) {
  background-color: transparent !important;
  position: relative;
  z-index: 3;
  /* 在背景图片上方 (Above background image) */
}

.map-container :deep(.mapboxgl-canvas-container canvas) {
  background-color: transparent !important;
}

/* 隐藏Mapbox标志和属性 (Hide Mapbox logo and attribution) */
.map-container :deep(.mapboxgl-ctrl-logo) {
  display: none !important;
}

.map-container :deep(.mapboxgl-ctrl-attrib) {
  display: none !important;
}

/* 标记样式 (Marker styles) */
.map-container :deep(.marker) {
  transition: all 0.3s ease;
}

.map-container :deep(.marker:hover) {
  transform: scale(1.2);
}

/* 流动粒子样式 (Flow particle styles) */
.map-container :deep(.flow-particle) {
  transition: opacity 0.1s ease;
}

/* 网格覆盖层样式 - 仅在启用时显示 (Grid overlay styles - only show when enabled) */
.map-container.show-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 15;
}

/* 发光效果 (Glow effect) */
.map-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
  pointer-events: none;
  z-index: 16;
}
</style>
